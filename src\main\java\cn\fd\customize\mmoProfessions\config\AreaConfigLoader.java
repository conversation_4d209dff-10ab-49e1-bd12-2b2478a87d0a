package cn.fd.customize.mmoProfessions.config;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.area.Area;
import cn.fd.customize.mmoProfessions.area.AreaBlock;
import cn.fd.customize.mmoProfessions.area.AreaProgress;
import cn.fd.customize.mmoProfessions.area.AreaRequirement;
import cn.fd.customize.mmoProfessions.area.AreaReward;
import cn.fd.customize.mmoProfessions.area.ProgressBar;
import cn.fd.customize.mmoProfessions.core.Profession;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 区域配置加载器
 */
public class AreaConfigLoader {

    private final Map<String, Area> areas;

    public AreaConfigLoader() {
        this.areas = new HashMap<>();
    }

    /**
     * 加载区域配置
     */
    public void loadAreas() {
        areas.clear();

        File areaFile = new File(MMOProfessions.getInstance().getDataFolder(), "area.yml");
        if (!areaFile.exists()) {
            MMOProfessions.getInstance().saveResource("area.yml", false);
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(areaFile);

        for (String areaId : config.getKeys(false)) {
            try {
                Area area = loadArea(areaId, config.getConfigurationSection(areaId));
                if (area != null) {
                    areas.put(areaId, area);
                }
            } catch (Exception e) {
                MMOProfessions.getInstance().getLogger().warning("加载区域 " + areaId + " 时出错: " + e.getMessage());
            }
        }

        MMOProfessions.getInstance().getLogger().info("总共加载了 " + areas.size() + " 个区域");
    }

    /**
     * 加载单个区域
     */
    private Area loadArea(String areaId, ConfigurationSection section) {
        if (section == null) {
            return null;
        }

        // 加载要求
        AreaRequirement requirement = loadRequirement(section.getConfigurationSection("require"));
        if (requirement == null) {
            MMOProfessions.getInstance().getLogger().warning("区域 " + areaId + " 缺少 require 配置");
            return null;
        }

        // 加载方块
        List<AreaBlock> blocks = loadBlocksList(section, "blocks");
        if (blocks == null || blocks.isEmpty()) {
            MMOProfessions.getInstance().getLogger().warning("区域 " + areaId + " 缺少 blocks 配置");
            return null;
        }

        // 加载刷新方块
        List<AreaBlock> refreshingBlocks = loadBlocksList(section, "refreshing_blocks");

        // 加载其他配置
        int maxBreakTime = section.getInt("max_break_times", 5);
        int refreshInterval = section.getInt("refresh_interval", 30);

        // 创建区域进度对象
        AreaProgress progress = new AreaProgress(areaId, maxBreakTime, refreshInterval);

        // 加载进度条配置
        ProgressBar progressBar = loadProgressBar(section.getConfigurationSection("progress_bar"));
        if (progressBar == null) {
            // 使用默认进度条配置
            progressBar = new ProgressBar(ProgressBar.DisplayType.SUBTITLE, "&6当前进度: &e[{0}/{1}]");
        }

        // 加载奖励配置
        List<AreaReward> rewards = loadRewardsList(section, "drops");

        return new Area(areaId, requirement, progress, blocks, refreshingBlocks, progressBar, rewards);
    }

    /**
     * 加载要求配置
     */
    private AreaRequirement loadRequirement(ConfigurationSection section) {
        if (section == null) {
            return null;
        }

        String professionId = section.getString("profession");
        int level = section.getInt("level", 1);
        List<?> tools = section.getList("tools");

        // 获取职业对象
        Profession profession = MMOProfessions.getProfessionManager().getProfession(professionId);

        return new AreaRequirement(profession, level, tools);
    }

    /**
     * 加载方块列表配置
     */
    private List<AreaBlock> loadBlocksList(ConfigurationSection section, String key) {
        List<AreaBlock> blocks = new ArrayList<>();

        if (section == null) {
            return blocks;
        }

        List<?> blockList = section.getList(key);
        if (blockList != null) {
            for (Object obj : blockList) {
                if (obj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> blockMap = (Map<String, Object>) obj;
                    AreaBlock block = parseBlockFromMap(blockMap);
                    if (block != null) {
                        blocks.add(block);
                    }
                }
            }
        }

        return blocks;
    }

    /**
     * 从Map解析方块
     */
    private AreaBlock parseBlockFromMap(Map<String, Object> blockMap) {
        String worldName = (String) blockMap.get("world");
        Object xObj = blockMap.get("x");
        Object yObj = blockMap.get("y");
        Object zObj = blockMap.get("z");
        String typeString = (String) blockMap.get("type");
        Object validObj = blockMap.get("valid");

        if (worldName == null || typeString == null || xObj == null || yObj == null || zObj == null) {
            MMOProfessions.getInstance().getLogger().warning("方块配置缺少必要字段");
            return null;
        }

        // 获取世界对象
        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            MMOProfessions.getInstance().getLogger().warning("无效的世界名称: " + worldName);
            return null;
        }

        int x = ((Number) xObj).intValue();
        int y = ((Number) yObj).intValue();
        int z = ((Number) zObj).intValue();
        boolean valid = validObj != null ? (Boolean) validObj : false;

        Material type;
        try {
            type = Material.valueOf(typeString.toUpperCase());
        } catch (IllegalArgumentException e) {
            MMOProfessions.getInstance().getLogger().warning("无效的方块类型: " + typeString);
            return null;
        }

        return new AreaBlock(world, x, y, z, type, valid);
    }

    /**
     * 加载进度条配置
     */
    private ProgressBar loadProgressBar(ConfigurationSection section) {
        if (section == null) {
            return null;
        }

        String typeString = section.getString("type", "subtitle");
        String text = section.getString("text", "当前进度 [{0}/{1}]");

        ProgressBar.DisplayType type;
        try {
            type = ProgressBar.DisplayType.valueOf(typeString.toUpperCase());
        } catch (IllegalArgumentException e) {
            type = ProgressBar.DisplayType.SUBTITLE;
        }

        return new ProgressBar(type, text);
    }

    /**
     * 加载奖励列表配置
     */
    private List<AreaReward> loadRewardsList(ConfigurationSection section, String key) {
        List<AreaReward> rewards = new ArrayList<>();

        if (section == null) {
            return rewards;
        }

        List<?> rewardList = section.getList(key);
        if (rewardList != null) {
            for (Object obj : rewardList) {
                if (obj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> rewardMap = (Map<String, Object>) obj;
                    AreaReward reward = parseRewardFromMap(rewardMap);
                    if (reward != null) {
                        rewards.add(reward);
                    }
                }
            }
        }

        return rewards;
    }

    /**
     * 从Map解析奖励配置
     */
    private AreaReward parseRewardFromMap(Map<String, Object> rewardMap) {
        Object chanceObj = rewardMap.get("chance");
        double chance = chanceObj != null ? ((Number) chanceObj).doubleValue() : 1.0;

        if (rewardMap.containsKey("item")) {
            Object item = rewardMap.get("item");
            return new AreaReward.ItemReward(item, chance);
        } else if (rewardMap.containsKey("exp")) {
            Object expObj = rewardMap.get("exp");
            int exp = expObj != null ? ((Number) expObj).intValue() : 0;
            String profession = (String) rewardMap.get("profession");
            return new AreaReward.ExpReward(exp, profession, chance);
        } else if (rewardMap.containsKey("commands")) {
            @SuppressWarnings("unchecked")
            List<String> commands = (List<String>) rewardMap.get("commands");
            return new AreaReward.CommandReward(commands, chance);
        }

        MMOProfessions.getInstance().getLogger().warning("无效的奖励配置: " + rewardMap);
        return null;
    }

    /**
     * 获取所有区域
     */
    public Map<String, Area> getAreas() {
        return new HashMap<>(areas);
    }

    /**
     * 获取指定区域
     */
    public Area getArea(String areaId) {
        return areas.get(areaId);
    }
}
