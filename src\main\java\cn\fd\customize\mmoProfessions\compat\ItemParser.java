package cn.fd.customize.mmoProfessions.compat;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 物品源解析器
 * 负责解析配置文件中的物品源配置，并创建对应的ItemSource对象
 */
public class ItemParser {
    /**
     * 从Map解析单个物品源
     *
     * @param configMap 配置Map，包含source和value字段
     * @return 解析出的ItemSource对象
     * @throws IllegalArgumentException 如果配置格式不正确
     */
    @SuppressWarnings("unchecked")
    public static ItemSource parseItemSource(Object configMap) {
        if (configMap == null) {
            throw new IllegalArgumentException("物品源配置不能为空");
        } else if (!(configMap instanceof Map)) {
            throw new IllegalArgumentException("物品源配置必须是Map类型");
        }

        try {

            Map<String, Object> map = (Map<String, Object>) configMap;

            Object source = map.get("source");
            Object value = map.get("value");
            Object amount = map.get("amount");

            if (source == null || value == null) {
                throw new IllegalArgumentException("物品源配置必须包含 'source' 和 'value' 字段");
            } else {
                String amountStr = amount != null ? amount.toString() : "1";
                return createItemSource(source.toString(), value.toString(), amountStr);
            }
        } catch (ClassCastException e) {
            throw new IllegalArgumentException("错误的物品源配置: " + configMap, e);
        }
    }

    /**
     * 创建物品源实例
     *
     * @param source 物品源类型
     * @param value  物品源值
     * @param amount 物品数量
     * @return 创建的物品源对象
     * @throws IllegalArgumentException 如果source类型不支持
     */
    private static ItemSource createItemSource(String source, String value, String amount) {
        if (source == null || value == null) {
            throw new IllegalArgumentException("物品源类型和值不能为空");
        }

        source = source.toLowerCase().trim();
        value = value.trim();
        if (amount == null) {
            amount = "1";
        }

        // 使用注册机制验证和创建物品源
        if (!ItemSourceRegistry.isSourceAvailable(source)) {
            throw new IllegalArgumentException("不支持的物品源类型或插件未安装: " + source);
        }

        return ItemSourceRegistry.createItemSource(source, value, amount);
    }

    /**
     * 创建物品源实例（不带数量参数，默认为1）
     *
     * @param source 物品源类型
     * @param value  物品源值
     * @return 创建的物品源对象
     * @throws IllegalArgumentException 如果source类型不支持
     */
    private static ItemSource createItemSource(String source, String value) {
        return createItemSource(source, value, "1");
    }

    /**
     * 从配置列表解析多个物品源
     * 
     * @param configList 配置列表，每个元素都是包含source和value的配置
     * @return 解析出的ItemSource列表
     */
    @SuppressWarnings("unchecked")
    public static List<ItemSource> parseItemSources(List<?> configList) {
        List<ItemSource> sources = new ArrayList<>();

        if (configList == null || configList.isEmpty()) {
            return sources;
        }

        for (Object configObj : configList) {
            try {
                ItemSource source;
                if (configObj instanceof Map) {
                    source = parseItemSource((Map<String, Object>) configObj);
                } else {
                    MMOProfessions.getInstance().getLogger().warning(
                            "不支持的物品源配置格式: " + configObj.getClass().getSimpleName());
                    continue;
                }

                sources.add(source);
            } catch (Exception e) {
                MMOProfessions.getInstance().getLogger().warning(
                        "解析物品源失败: " + configObj + " - " + e.getMessage());
            }
        }

        return sources;
    }

    /**
     * 解析数量字符串为int数组
     * 支持单纯数字或区间格式（如"1-5"）
     *
     * @param amount 数量字符串
     * @return int数组，[0]为最小值，[1]为最大值。如果是单个数字，两个值相同
     */
    public static int[] parseAmount(String amount) {
        if (amount == null || amount.trim().isEmpty()) {
            return new int[] { 1, 1 }; // 默认数量为1
        }

        amount = amount.trim();

        // 检查是否是区间格式
        if (amount.contains("-")) {
            String[] parts = amount.split("-", 2);
            if (parts.length == 2) {
                try {
                    int min = Integer.parseInt(parts[0].trim());
                    int max = Integer.parseInt(parts[1].trim());

                    if (min > max) {
                        // 如果最小值大于最大值，交换它们
                        int temp = min;
                        min = max;
                        max = temp;
                    }

                    return new int[] { min, max };
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("无效的区间格式: " + amount);
                }
            }
        }

        // 单纯数字
        try {
            int value = Integer.parseInt(amount);
            return new int[] { value, value };
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的数量格式: " + amount);
        }
    }

    /**
     * 从int数组中随机生成一个数量
     *
     * @param amountRange int数组，[0]为最小值，[1]为最大值
     * @return 随机生成的数量
     */
    public static int randomAmount(int[] amountRange) {
        if (amountRange == null || amountRange.length < 2) {
            return 1; // 默认数量为1
        }

        int min = amountRange[0];
        int max = amountRange[1];

        if (min == max) {
            return min;
        }

        // 在区间内随机生成一个数
        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }

    /**
     * 获取支持的物品源类型列表
     *
     * @return 支持的物品源类型
     */
    public static List<String> getSupportedSources() {
        return new ArrayList<>(ItemSourceRegistry.getRegisteredSources());
    }
}
