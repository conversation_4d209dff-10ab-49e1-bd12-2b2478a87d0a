package cn.fd.customize.mmoProfessions.compat;

import io.lumine.mythic.lib.MythicLib;
import io.lumine.mythic.lib.api.MMOLineConfig;
import io.lumine.mythic.lib.api.item.NBTItem;
import net.Indyuce.mmoitems.MMOItems;
import net.Indyuce.mmoitems.api.crafting.ingredient.Ingredient;
import net.Indyuce.mmoitems.api.crafting.ingredient.IngredientType;
import net.Indyuce.mmoitems.api.crafting.ingredient.inventory.PlayerIngredient;
import net.Indyuce.mmoitems.api.crafting.output.RecipeOutput;
import net.Indyuce.mmoitems.api.player.RPGPlayer;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * MMOItems物品源实现
 * 处理MMOItems插件的物品
 */
public class MMOItemsSource extends AbstractItemSource {

    private final Ingredient<?> ingredient;
    private final RecipeOutput output;

    @SuppressWarnings("deprecation")
    public MMOItemsSource(ItemSourceFactory factory, String value, String amount) {
        super(factory, value, amount);
        this.ingredient = MMOItems.plugin.getCrafting().getIngredient(new MMOLineConfig(value));
        this.ingredient.setAmount(getRequiredAmount());
        this.output = MMOItems.plugin.getCrafting().getRecipeOutput(value);
    }

    public MMOItemsSource(ItemSourceFactory factory, String value) {
        this(factory, value, "1");
    }

    @Override
    public boolean matchesIngredient(ItemStack item) {
        PlayerIngredient target = getPlayerIngredient(item);
        return matchesIngredient(target, ingredient);
    }

    @Override
    public ItemStack generateOutput(Player player) {
        return applyAmount(output.getOutput(getRPGPlayer(player)));
    }

    @SuppressWarnings("unchecked")
    public static boolean matchesIngredient(PlayerIngredient target, Ingredient<?> ingredient) {
        if (target == null) {
            return false;
        }
        try {
            if (((Ingredient<PlayerIngredient>) ingredient).matches(target)) {
                return true;
            }
        } catch (Throwable ignored) {
        }
        return false;
    }

    /**
     * 获取 MMOItems 的 Ingredient
     */
    public static PlayerIngredient getPlayerIngredient(ItemStack input) {
        if (input == null || input.getType().isAir()) {
            return null;
        }

        NBTItem nbt = MythicLib.plugin.getVersion().getWrapper().getNBTItem(input);
        // 寻找类型
        IngredientType<?> ingredientType = null;
        for (IngredientType<?> type : MMOItems.plugin.getCrafting().getIngredients()) {
            if (type.check(nbt)) {
                ingredientType = type;
                break;
            }
        }

        // 获取 Ingredient
        if (ingredientType == null) {
            throw new RuntimeException("No ingredient type found for " + nbt.getType());
        }

        return ingredientType.readPlayerIngredient(nbt);
    }

    /**
     * 获取玩家的 MMOItems RPGPlayer
     */
    public static RPGPlayer getRPGPlayer(Player player) {
        return MMOItems.plugin.getPlayerDataManager().get(player).getRPG();
    }

}
