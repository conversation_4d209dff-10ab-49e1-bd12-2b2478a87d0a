package cn.fd.customize.mmoProfessions.commands;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.alchemy.AlchemyTask;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 炼金系统命令处理器
 * 处理所有炼金相关的命令，包括打开GUI、查看状态、管理任务等
 */
public class AlchemyCommand implements CommandExecutor, TabCompleter {

    public AlchemyCommand() {
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c只有玩家可以使用此命令！");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // 打开炼金主界面
            openAlchemyGUI(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "open":
            case "gui":
                openAlchemyGUI(player);
                break;

            case "status":
                if (!player.hasPermission("mmoprofessions.admin")) {
                    MMOProfessions.getMessageManager().sendMessage(player, "general.no_permission");
                    return true;
                }
                showPlayerStatus(player);
                break;

            case "cancel":
                if (!player.hasPermission("mmoprofessions.admin")) {
                    MMOProfessions.getMessageManager().sendMessage(player, "general.no_permission");
                    return true;
                }
                if (args.length < 2) {
                    player.sendMessage("§c用法: /alchemy cancel <队列编号>");
                    return true;
                }
                cancelTask(player, args[1]);
                break;

            case "clear":
                if (!player.hasPermission("mmoprofessions.admin")) {
                    MMOProfessions.getMessageManager().sendMessage(player, "general.no_permission");
                    return true;
                }
                clearCompletedTasks(player, args);
                break;

            case "info":
                if (!player.hasPermission("mmoprofessions.admin")) {
                    MMOProfessions.getMessageManager().sendMessage(player, "general.no_permission");
                    return true;
                }
                if (args.length < 2) {
                    player.sendMessage("§c用法: /alchemy info <队列编号>");
                    return true;
                }
                showTaskInfo(player, args[1]);
                break;

            case "unlock":
                if (!player.hasPermission("mmoprofessions.admin")) {
                    MMOProfessions.getMessageManager().sendMessage(player, "general.no_permission");
                    return true;
                }
                if (args.length < 2) {
                    player.sendMessage("§c用法: /alchemy unlock <队列编号> [玩家名]");
                    return true;
                }
                unlockQueue(player, args);
                break;

            case "help":
                if (!player.hasPermission("mmoprofessions.alchemy.use")) {
                    MMOProfessions.getMessageManager().sendMessage(player, "general.no_permission");
                    return true;
                }
                showHelp(player);
                break;

            default:
                // 无权限玩家使用未知命令时直接打开GUI
                if (!player.hasPermission("mmoprofessions.admin")) {
                    openAlchemyGUI(player);
                } else {
                    showHelp(player);
                }
                break;
        }

        return true;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!(sender instanceof Player)) {
            return new ArrayList<>();
        }

        Player player = (Player) sender;

        if (args.length == 1) {
            List<String> completions = new ArrayList<>();

            // 所有玩家都可以使用的基础命令
            completions.addAll(Arrays.asList("open", "gui"));

            // 管理员命令
            if (player.hasPermission("mmoprofessions.admin")) {
                completions.addAll(Arrays.asList("status", "cancel", "info", "help", "clear", "unlock"));
            }

            return completions;
        }

        if (args.length == 2 && (args[0].equalsIgnoreCase("cancel") ||
                args[0].equalsIgnoreCase("info") || args[0].equalsIgnoreCase("unlock"))) {
            // 检查权限
            if (!player.hasPermission("mmoprofessions.admin")) {
                return new ArrayList<>();
            }

            List<String> queueNumbers = new ArrayList<>();
            int maxQueue = getMaxQueueIndex();
            for (int i = 0; i < maxQueue; i++) {
                queueNumbers.add(String.valueOf(i));
            }
            return queueNumbers;
        }

        if (args.length == 2 && args[0].equalsIgnoreCase("clear")) {
            // 检查管理员权限
            if (!player.hasPermission("mmoprofessions.admin")) {
                return new ArrayList<>();
            }
            return Arrays.asList("all", "completed", "failed");
        }

        return new ArrayList<>();
    }

    /**
     * 打开炼金GUI
     */
    private void openAlchemyGUI(Player player) {
        // 检查玩家是否有炼金职业
        if (!MMOProfessions.getProfessionManager().hasProfession(player, "alchemy")) {
            player.sendMessage("§c你还没有炼金职业！");
            return;
        }

        MMOProfessions.getAlchemyManager().getMainGUI().openGUI(player);
    }

    /**
     * 显示玩家状态
     */
    private void showPlayerStatus(Player player) {
        // 获取玩家所有任务
        Map<Integer, AlchemyTask> allTasks = MMOProfessions.getAlchemyManager().getTaskManager()
                .getPlayerTasks(player.getUniqueId());

        if (allTasks.isEmpty()) {
            MMOProfessions.getMessageManager().sendMessage(player, "alchemy.no_tasks");
            return;
        }

        MMOProfessions.getMessageManager().sendMessage(player, "alchemy.status_header");

        // 按队列分组显示
        for (int i = 0; i < getMaxQueueIndex(); i++) {
            AlchemyTask queueTask = MMOProfessions.getAlchemyManager().getTaskManager()
                    .getPlayerQueueTask(player.getUniqueId(), i);
            if (queueTask != null && !queueTask.isCompleted()) {
                MMOProfessions.getMessageManager().sendMessage(player, "alchemy.queue_status",
                        String.valueOf(i), "1");

                String status = queueTask.isCompleted() ? "已完成" : "进行中";
                MMOProfessions.getMessageManager().sendMessage(player, "alchemy.task_info",
                        String.valueOf(queueTask.getTaskId()).substring(0,
                                Math.min(8, String.valueOf(queueTask.getTaskId()).length())),
                        status,
                        String.valueOf(queueTask.getRemainingTime()));
            }
        }

        // 显示已完成任务
        long completedCount = allTasks.values().stream().filter(AlchemyTask::isCompleted).count();
        if (completedCount > 0) {
            MMOProfessions.getMessageManager().sendMessage(player, "alchemy.completed_tasks",
                    String.valueOf(completedCount));
        }
    }

    /**
     * 取消指定队列的任务
     */
    private void cancelTask(Player player, String queueStr) {
        try {
            int queueIndex = Integer.parseInt(queueStr);
            int maxQueue = getMaxQueueIndex();
            if (queueIndex < 0 || queueIndex >= maxQueue) {
                player.sendMessage("§c队列编号必须在0-" + (maxQueue - 1) + "之间！");
                return;
            }

            boolean success = MMOProfessions.getAlchemyManager().getTaskManager()
                    .cancelTask(player.getUniqueId(), queueIndex);

            if (success) {
                MMOProfessions.getMessageManager().sendMessage(player, "alchemy.task_cancelled");
            } else {
                player.sendMessage("§c队列 " + queueIndex + " 中没有可取消的任务！");
            }
        } catch (NumberFormatException e) {
            player.sendMessage("§c队列编号必须是数字！");
        }
    }

    /**
     * 清理已完成的任务
     */
    private void clearCompletedTasks(Player player, String[] args) {
        String type = args.length > 1 ? args[1] : "completed";

        switch (type.toLowerCase()) {
            case "all":
                if (args.length > 2) {
                    // 清理指定玩家的任务
                    Player target = Bukkit.getPlayer(args[2]);
                    if (target == null) {
                        player.sendMessage("§c玩家 " + args[2] + " 不在线！");
                        return;
                    }
                    clearPlayerTasks(player, target, true, true);
                } else {
                    player.sendMessage("§c用法: /alchemy clear all <玩家名>");
                }
                break;
            case "completed":
                clearPlayerTasks(player, player, true, false);
                break;
            case "failed":
                clearPlayerTasks(player, player, false, true);
                break;
            default:
                player.sendMessage("§c用法: /alchemy clear <all|completed|failed> [玩家名]");
                break;
        }
    }

    /**
     * 清理玩家任务的辅助方法
     */
    private void clearPlayerTasks(Player sender, Player target, boolean clearCompleted, boolean clearFailed) {
        Map<Integer, AlchemyTask> tasks = MMOProfessions.getAlchemyManager().getTaskManager()
                .getPlayerTasks(target.getUniqueId());

        int clearedCount = 0;
        List<Integer> toRemove = new ArrayList<>();

        for (Map.Entry<Integer, AlchemyTask> entry : tasks.entrySet()) {
            AlchemyTask task = entry.getValue();
            if (task.isCompleted()) {
                boolean shouldRemove = false;
                if (clearCompleted && !task.getOutputItems().isEmpty()) {
                    shouldRemove = true;
                } else if (clearFailed && task.getOutputItems().isEmpty()) {
                    shouldRemove = true;
                }

                if (shouldRemove) {
                    toRemove.add(entry.getKey());
                    clearedCount++;
                }
            }
        }

        for (Integer queueIndex : toRemove) {
            MMOProfessions.getAlchemyManager().getTaskManager()
                    .removeCompletedEmptyTask(target.getUniqueId(), queueIndex);
        }

        if (sender.equals(target)) {
            sender.sendMessage("§a已清理 " + clearedCount + " 个任务！");
        } else {
            sender.sendMessage("§a已清理玩家 " + target.getName() + " 的 " + clearedCount + " 个任务！");
        }
    }

    /**
     * 显示指定队列任务的详细信息
     */
    private void showTaskInfo(Player player, String queueStr) {
        try {
            int queueIndex = Integer.parseInt(queueStr);
            int maxQueue = getMaxQueueIndex();
            if (queueIndex < 0 || queueIndex >= maxQueue) {
                player.sendMessage("§c队列编号必须在0-" + (maxQueue - 1) + "之间！");
                return;
            }

            AlchemyTask task = MMOProfessions.getAlchemyManager().getTaskManager()
                    .getPlayerQueueTask(player.getUniqueId(), queueIndex);

            if (task == null) {
                player.sendMessage("§c队列 " + queueIndex + " 中没有任务！");
                return;
            }

            player.sendMessage("§e=== 队列 " + queueIndex + " 任务信息 ===");
            player.sendMessage("§7状态: " + (task.isCompleted() ? "§a已完成" : "§e进行中"));

            if (!task.isCompleted()) {
                String timeRemaining = MMOProfessions.getAlchemyManager().getConfigManager()
                        .formatRemainingTime(task.getRemainingTime());
                player.sendMessage("§7剩余时间: §e" + timeRemaining);

                double progress = task.calculateProgress() * 100;
                player.sendMessage("§7进度: §e" + String.format("%.1f", progress) + "%");
            }

            player.sendMessage("§7输入物品数量: §e" + task.getInputItems().size());
            player.sendMessage("§7输出物品数量: §e" + task.getOutputItems().size());

            if (task.getStabilizer() != null) {
                player.sendMessage("§7稳定剂: §e" + task.getStabilizer().getType().name());
            }
        } catch (NumberFormatException e) {
            player.sendMessage("§c队列编号必须是数字！");
        }
    }

    /**
     * 获取最大队列索引（从配置文件中的queue_button_slots长度获取）
     */
    private int getMaxQueueIndex() {
        return MMOProfessions.getAlchemyManager().getConfigManager().getMaxQueueCount();
    }

    /**
     * 解锁队列
     */
    private void unlockQueue(Player sender, String[] args) {
        try {
            int queueIndex = Integer.parseInt(args[1]);
            int maxQueue = getMaxQueueIndex();
            if (queueIndex < 0 || queueIndex >= maxQueue) {
                sender.sendMessage("§c队列编号必须在0-" + (maxQueue - 1) + "之间！");
                return;
            }

            Player target = sender;
            if (args.length > 2) {
                // 指定了目标玩家
                target = Bukkit.getPlayer(args[2]);
                if (target == null) {
                    sender.sendMessage("§c玩家 " + args[2] + " 不在线！");
                    return;
                }
            }

            // 检查队列是否已经解锁
            if (MMOProfessions.getAlchemyManager().getTaskManager().isQueueUnlocked(target.getUniqueId(), queueIndex)) {
                sender.sendMessage("§c队列 " + queueIndex + " 已经解锁了！");
                return;
            }

            // 检查是否是下一个要解锁的队列
            int currentUnlocked = MMOProfessions.getAlchemyManager().getTaskManager().getUnlockedQueueCount(target.getUniqueId());
            if (queueIndex != currentUnlocked) {
                sender.sendMessage("§c只能按顺序解锁队列！下一个可解锁的队列是: " + currentUnlocked);
                return;
            }

            // 解锁队列
            boolean success = MMOProfessions.getAlchemyManager().getTaskManager().unlockNextQueue(target.getUniqueId());
            if (success) {
                if (sender.equals(target)) {
                    sender.sendMessage("§a成功解锁队列 " + queueIndex + "！");
                } else {
                    sender.sendMessage("§a成功为玩家 " + target.getName() + " 解锁队列 " + queueIndex + "！");
                    target.sendMessage("§a管理员为你解锁了队列 " + queueIndex + "！");
                }
            } else {
                sender.sendMessage("§c解锁失败！可能已达到最大队列数。");
            }
        } catch (NumberFormatException e) {
            sender.sendMessage("§c队列编号必须是数字！");
        }
    }

    /**
     * 显示帮助信息
     */
    private void showHelp(Player player) {
        player.sendMessage("§e=== 炼金系统命令帮助 ===");
        player.sendMessage("§7/alchemy §f- 打开炼金GUI");
        player.sendMessage("§7/alchemy open §f- 打开炼金GUI");

        if (player.hasPermission("mmoprofessions.admin")) {
            player.sendMessage("§7/alchemy status §f- 查看任务状态");
            player.sendMessage("§7/alchemy cancel <队列> §f- 取消指定队列的任务");
            player.sendMessage("§7/alchemy info <队列> §f- 查看指定队列任务详情");
            player.sendMessage("§7/alchemy help §f- 显示此帮助");
            player.sendMessage("§7/alchemy clear <类型> §f- 清理已完成的任务");
            player.sendMessage("§7/alchemy clear all <玩家> §f- 清理指定玩家的所有任务");
        }
    }

}
