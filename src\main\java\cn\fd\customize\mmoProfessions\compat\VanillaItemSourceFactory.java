package cn.fd.customize.mmoProfessions.compat;

/**
 * 原版物品源工厂
 */
public class VanillaItemSourceFactory implements ItemSourceFactory {
    
    @Override
    public ItemSource create(String value, String amount) {
        return new VanillaItemSource(this, value, amount);
    }

    @Override
    public boolean isAvailable() {
        return true; // 原版物品总是可用
    }

    @Override
    public String getSourceType() {
        return "vanilla";
    }
}
